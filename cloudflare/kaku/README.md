# Kaku - Connection Agent

## Overview

Kaku is a Cloudflare Agent-powered service that enables users to connect to various third-party services (Facebook, Gmail, Amazon, Spotify, Netflix, etc.) through a dynamic, AI-generated interface. The system discovers login flows on-the-fly and manages the authentication process, eliminating the need for predefined integration knowledge.

### 🎥 WebRTC Streaming System

Kaku features a robust **WebRTC-based streaming architecture** that provides real-time video streaming from remote browser sessions with comprehensive stream interruption handling:

- **Real-time video streaming** with sub-second latency
- **Automatic stream recovery** during page navigation and social login flows
- **Visual status indicators** with loading spinners for connection states
- **Intelligent interruption detection** for track ended, navigation, and connection loss events
- **Seamless reconnection** with exponential backoff and automatic restoration

The streaming system handles complex scenarios like social login redirects, page reloads, and network interruptions while maintaining a smooth user experience through visual feedback and automatic recovery mechanisms.

## Architecture

![img.png](kaku-architecture-diagram.png)

```mermaid
graph TD
    User[User] <--> |HTMX/WebSockets| Connection[Connection Agent]
    Connection <--> |Browser Context| Browserbase[Browserbase]
    Browserbase <--> |Page Interaction| Services[External Services]
    Connection <--> |AI Prompts| LLM[LLM via Agents AI SDK]
    LLM --> |HTMX Generation| Connection

    subgraph "Worker (Hono)"
        Connection
        LLM
    end

    subgraph "Vercel"
        Browserbase
    end

    subgraph "Third Party"
        Services
    end
```

### Key Components

#### 1. Connection Agent (Cloudflare Agents)

- Stateful service built on Cloudflare's Agent SDK and Durable Objects
- Manages authentication state and service-specific logic
- Each service gets its own dedicated agent instance (e.g., `api/connections/facebook`)
- Generates dynamic HTMX interfaces using LLM-powered code generation

#### 2. Browserbase Integration

- Remote browser service running on Vercel
- Handles actual browser interactions with third-party services
- Provides page context (forms, fields, buttons) back to the Connection Agent
- Executes field filling and submission based on user input

#### 3. LLM Integration

- Uses Cloudflare Agents AI SDK for LLM access
- Transforms browser context into user-friendly HTMX interfaces
- Adapts to different service requirements dynamically
- No predefined knowledge base required - works with just service URL

#### 4. HTMX Frontend

- Lightweight, JavaScript-free interactive interfaces
- Real-time updates via WebSockets
- Dynamic form generation specific to each service
- Seamless user experience with minimal client-side code

### Data Flow

1. User requests connection to a service (e.g., Facebook)
2. Connection Agent initializes and sends the login URL to Browserbase
3. Browserbase loads the page and returns context (form fields, layout)
4. LLM processes this context and generates HTMX interface
5. User interacts with the HTMX interface, providing credentials
6. Connection Agent forwards credentials to Browserbase
7. Browserbase completes the authentication flow
8. Connection status and results are streamed back to the user

## Agent URL Mapping

Our Hono app uses a middleware called `agentsMiddleware` to map agent class names to URL routes automatically. Here's how it works:

The `agentsMiddleware` function:

1. Detects whether the incoming request is a WebSocket connection or standard HTTP request
2. Routes the request to the appropriate agent
3. Handles WebSocket upgrades for persistent connections

![img_1.png](agents-middleware-architecture.png)

## Adding Puppeteer Client Scripts

To inject client-side scripts via Puppeteer:
Create a file in `src/client/`, e.g. `my-script.mjs`.

Update Rollup config:
`standardConfig('src/client/my-script.mjs', 'my-script')`

Build:
npm run build-client

Inject in Puppeteer:

```js
const res = await fetch(`${this.env.KAKU_API_ENDPOINT}/out/my-script.min.js`);
const script = await res.text();

await this.kazeelClient.send('Page.addScriptToEvaluateOnNewDocument', {
  source: script,
});
```

### URL Transformation Rules

1. **Class to URL Transformation**:
   - Agent class names are transformed to kebab-case in URLs
   - Capital letters become lowercase and are preceded by hyphens
   - Example: `ConnectionAgent` → `/api/connection-agent/[instanceId]`
   - In our implementation: `Connections` → `/api/connections/[instanceId]`

2. **URL Anatomy**:
   - `/api` - Default prefix (configurable via middleware options)
   - `/connections` - Kebab-cased agent class name
   - `/[instanceId]` - Unique identifier for a specific agent instance (e.g., service name)

The `agentsMiddleware` handles HTTP and WebSocket connections to these endpoints, routing them to the appropriate agent instances based on the URL path.

## Development

### Prerequisites

- Node.js 18+
- Wrangler CLI

### Setup

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Testing

```bash
npm test
```

## WebRTC Streaming Documentation

For detailed information about the WebRTC streaming system, see:

- **[WebRTC Streaming Architecture](./docs/webrtc-streaming-architecture.md)** - Comprehensive technical documentation including:
  - System architecture and component overview
  - Sequence diagrams for connection establishment and navigation flows
  - Timing considerations and performance metrics
  - Error handling and recovery mechanisms
  - Configuration options and best practices
  - Troubleshooting guide and debug commands

### Stream States and Recovery

The system provides visual feedback for all connection states:

| State                 | Description             | Visual Indicator |
| --------------------- | ----------------------- | ---------------- |
| `connecting`          | Establishing connection | Blue spinner     |
| `connected`           | Stream active           | Green checkmark  |
| `reconnecting`        | Restoring connection    | Orange spinner   |
| `navigation-detected` | Page reload in progress | Purple spinner   |
| `failed`              | Connection failed       | Red error icon   |

### Key Features

- **Automatic Recovery**: Stream interruptions are detected and handled automatically
- **Visual Feedback**: Users see clear status indicators during connection changes
- **Social Login Support**: Handles navigation events during OAuth flows
- **Performance Optimized**: Efficient resource management and cleanup
- **Comprehensive Logging**: Debug-friendly logging for development and monitoring
