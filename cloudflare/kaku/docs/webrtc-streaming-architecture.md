# WebRTC-Based Social Media Streaming Architecture

## Overview

This document provides a comprehensive guide to the WebRTC-based streaming system implemented for social media automation. The system enables real-time video streaming from remote browser sessions to the client interface, with robust handling of page navigation events and stream interruptions.

## Architecture Components

### Core Components

1. **Tab Streamer (`tab-streamer.ts`)** - Client-side script injected into remote browser tabs
2. **Connection Agent (`connection-agent.ts`)** - Server-side agent managing browser sessions
3. **UI Layout (`layout.ts`)** - Frontend interface with video display and stream status
4. **Stream Status Component (`stream-status.ts`)** - UI component for connection state visualization

### Technology Stack

- **WebRTC**: Peer-to-peer video streaming
- **WebSocket**: Signaling server for connection establishment
- **getDisplayMedia API**: Screen capture from browser tabs
- **Chrome DevTools Protocol (CDP)**: Browser automation and script injection

## System Flow

### 1. Initial Connection Establishment

```mermaid
sequenceDiagram
    participant UI as Client UI
    participant Agent as Connection Agent
    participant Browser as Remote Browser
    participant Tab as Tab Streamer

    UI->>Agent: WebSocket connection
    Agent->>Browser: Create browser session
    Agent->>Browser: Inject tab-streamer script
    Browser->>Tab: Initialize tab streamer
    Tab->>Agent: WebSocket connection
    Tab->>UI: WebRTC offer via Agent
    UI->>Tab: WebRTC answer via Agent
    Tab->>UI: Video stream (WebRTC)
```

### 2. Social Login Navigation Flow

```mermaid
sequenceDiagram
    participant UI as Client UI
    participant Agent as Connection Agent
    participant Browser as Remote Browser
    participant Tab as Tab Streamer

    Browser->>Agent: Frame navigation detected
    Agent->>Agent: Check if social login provider
    Agent->>Browser: Inject persistent scripts
    Browser->>Tab: Page reload/navigation
    Tab->>Tab: Scripts lost during navigation
    Browser->>Tab: Re-inject scripts (persistent)
    Tab->>Agent: Signal reconnection
    Agent->>UI: Broadcast reconnection event
    UI->>UI: Show navigation overlay
    Tab->>UI: Re-establish WebRTC connection
    UI->>UI: Hide overlay, restore video
```

### 3. Stream Interruption and Recovery

```mermaid
sequenceDiagram
    participant UI as Client UI
    participant Tab as Tab Streamer
    participant Stream as Media Stream

    Stream->>Tab: Track ended event
    Tab->>Tab: Detect stream interruption
    Tab->>UI: Connection state change
    UI->>UI: Show reconnection overlay
    Tab->>Tab: Attempt reconnection
    Tab->>Stream: Request new media stream
    Stream->>Tab: New stream available
    Tab->>UI: Stream restored event
    UI->>UI: Hide overlay, restore video
```

## Timing Considerations

### Stream Pause Durations

| Event Type | Expected Duration | Recovery Time |
|------------|------------------|---------------|
| Page Navigation | 2-5 seconds | 3-7 seconds |
| Social Login Redirect | 1-3 seconds | 2-5 seconds |
| Network Reconnection | 5-10 seconds | 10-15 seconds |
| Script Re-injection | 1-2 seconds | 2-4 seconds |

### Script Re-injection Timing

1. **Phase 1: Immediate Injection** - Scripts injected into current page (~500ms)
2. **Phase 2: Persistent Injection** - Scripts set up for future navigation (~200ms)
3. **Navigation Event** - Page reloads, scripts lost (~1-3 seconds)
4. **Re-injection** - Persistent scripts activate (~1-2 seconds)
5. **Stream Restoration** - WebRTC connection re-established (~2-4 seconds)

### WebRTC Reconnection Timeouts

- **Initial Connection**: 30 seconds timeout
- **ICE Gathering**: 10 seconds timeout
- **Reconnection Attempts**: 5 attempts with exponential backoff
- **Backoff Intervals**: 1s, 2s, 4s, 8s, 16s

## Error Handling and Recovery

### Stream Interruption Types

1. **Track Ended** - Media track stops unexpectedly
   - Cause: User stops screen sharing, browser tab closed
   - Recovery: Request new media stream, re-establish connection

2. **Navigation** - Page reload or redirect
   - Cause: Social login redirects, form submissions
   - Recovery: Re-inject scripts, signal reconnection

3. **Connection Lost** - WebRTC or WebSocket failure
   - Cause: Network issues, server problems
   - Recovery: Exponential backoff reconnection attempts

### Recovery Mechanisms

```typescript
// Stream interruption detection
videoTrack.addEventListener('ended', () => {
  handleStreamInterruption('track-ended');
});

// WebRTC connection monitoring
pc.onconnectionstatechange = () => {
  switch (pc.connectionState) {
    case 'disconnected':
      handleStreamInterruption('connection-lost');
      break;
    case 'failed':
      updateConnectionState('failed');
      break;
  }
};

// Automatic reconnection
async function attemptReconnection() {
  if (reconnectAttempts < maxReconnectAttempts) {
    await new Promise(resolve => 
      setTimeout(resolve, 1000 * reconnectAttempts)
    );
    // Re-establish connections...
  }
}
```

### UI State Management

The system provides visual feedback for all connection states:

- **Connecting**: Blue spinner with "Establishing connection..."
- **Connected**: Green checkmark with "Stream connected"
- **Reconnecting**: Orange spinner with "Reconnecting stream..."
- **Navigation Detected**: Purple spinner with navigation message
- **Failed**: Red error icon with failure message

## Performance Considerations

### Optimization Strategies

1. **Frame Rate Optimization**
   - Default: 15 FPS for balance of quality and performance
   - Adjustable based on network conditions
   - Automatic quality degradation during poor connections

2. **Stream Processing**
   - Hardware-accelerated video encoding when available
   - Efficient frame cropping for captcha detection
   - Minimal CPU usage through WebRTC native processing

3. **Connection Management**
   - Connection pooling for multiple browser sessions
   - Efficient cleanup of resources on disconnection
   - Proactive connection health monitoring

4. **Script Injection Efficiency**
   - Minimal script size through bundling and minification
   - Lazy loading of non-essential components
   - Cached script URLs to reduce network requests

### Resource Management

```typescript
// Cleanup on disconnection
function stopStreaming() {
  if (screenStream) {
    screenStream.getTracks().forEach(track => {
      if (track.readyState === 'live') {
        track.stop();
      }
    });
  }
  
  if (pc) {
    pc.close();
  }
  
  if (socket) {
    socket.close();
  }
}
```

### Memory Optimization

- Automatic cleanup of video frames after processing
- Efficient MediaStream management
- Proper disposal of WebRTC connections
- Garbage collection of unused DOM elements

## Configuration Options

### Tab Streamer Configuration

```typescript
interface StreamingConfig {
  mode: 'FULL_SCREEN' | 'CROPPED';
  frameRate: number;           // Default: 15
  debug: boolean;              // Default: false
  enableInputDetection: boolean; // Default: false
  wsEndpoint?: string;
}
```

### Connection Timeouts

```typescript
const connectionConfig = {
  maxReconnectAttempts: 5,
  connectionTimeout: 30000,    // 30 seconds
  iceGatheringTimeout: 10000,  // 10 seconds
  frameRequestTimeout: 5000,   // 5 seconds
};
```

## Monitoring and Debugging

### Logging Framework

The system includes comprehensive logging with configurable levels:

```typescript
// Enable debug logging
const config = { debug: true };

// Log levels: log, warn, error
log('WebRTC connection established');
warn('Connection unstable, monitoring...');
error('Failed to establish connection');
```

### Connection State Monitoring

Real-time monitoring of connection health:

- WebRTC connection state tracking
- WebSocket readiness monitoring
- Media track status verification
- Frame delivery rate measurement

### Performance Metrics

Key metrics tracked for optimization:

- Connection establishment time
- Stream restoration duration
- Frame delivery rate
- Reconnection success rate
- Resource usage (CPU, memory, bandwidth)

## Best Practices

### Development Guidelines

1. **Error Handling**: Always implement comprehensive error handling for WebRTC operations
2. **Resource Cleanup**: Properly dispose of media streams and connections
3. **State Management**: Maintain clear separation between connection and UI state
4. **Logging**: Use structured logging for debugging and monitoring
5. **Testing**: Test with various network conditions and browser configurations

### Production Deployment

1. **STUN/TURN Servers**: Configure reliable STUN/TURN servers for NAT traversal
2. **SSL/TLS**: Ensure all connections use secure protocols
3. **Monitoring**: Implement comprehensive monitoring and alerting
4. **Scaling**: Design for horizontal scaling of connection agents
5. **Fallbacks**: Implement fallback mechanisms for critical failures

## Troubleshooting Guide

### Common Issues

1. **Stream Not Appearing**
   - Check WebRTC connection state
   - Verify media permissions
   - Confirm script injection success

2. **Frequent Disconnections**
   - Monitor network stability
   - Check STUN/TURN server configuration
   - Verify firewall settings

3. **Slow Reconnection**
   - Optimize script injection timing
   - Reduce reconnection backoff intervals
   - Check server response times

### Debug Commands

```javascript
// Check connection state
console.log('WebRTC state:', pc.connectionState);
console.log('WebSocket state:', socket.readyState);

// Monitor stream health
console.log('Video tracks:', screenStream.getVideoTracks());
console.log('Stream active:', screenStream.active);

// Test script injection
console.log('Tab streamer available:', !!window.tabStreamer);
```

This architecture provides a robust foundation for real-time browser automation with comprehensive error handling and recovery mechanisms.
