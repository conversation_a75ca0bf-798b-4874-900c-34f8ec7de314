# WebRTC Streaming Flow Documentation

## Overview

This document explains how the WebRTC streaming system works, focusing on the core flows for initial setup and page reload recovery. The system maintains continuous video streaming from remote browser tabs even when pages reload during social login flows.

## Core Components

- **Tab Streamer Script**: Injected into browser tabs to capture and stream video
- **Connection Agent**: Server-side component managing browser sessions
- **Client UI**: Frontend that displays the video stream and connection status

## Initial Setup Flow

The streaming system follows these steps to establish the initial connection:

### 1. <PERSON><PERSON>t Injection

```mermaid
sequenceDiagram
    participant Agent as Connection Agent
    participant Browser as Remote Browser
    participant Tab as Tab Streamer

    Agent->>Browser: Inject tab-streamer script
    Browser->>Tab: Script loaded and initialized
    Tab->>Tab: Initialize streaming configuration
```

The Connection Agent injects the tab-streamer script into the remote browser tab using CDP (Chrome DevTools Protocol).

### 2. Persistent Script Setup

```mermaid
sequenceDiagram
    participant Agent as Connection Agent
    participant Browser as Remote Browser

    Agent->>Browser: addScriptToEvaluateOnNewDocument()
    Browser->>Browser: Register persistent script for future page loads
    Note over <PERSON><PERSON><PERSON>: <PERSON><PERSON><PERSON> will auto-inject on any page reload
```

The agent calls `addScriptToEvaluateOnNewDocument()` to ensure the tab-streamer script automatically re-injects whenever the page reloads.

### 3. WebSocket Connection

```mermaid
sequenceDiagram
    participant Tab as Tab Streamer
    participant Agent as Connection Agent
    participant UI as Client UI

    Tab->>Agent: WebSocket connection established
    Agent->>UI: Forward connection ready signal
    UI->>UI: Update status: "Connecting..."
```

The tab streamer establishes a WebSocket connection to the Connection Agent for signaling.

### 4. WebRTC Negotiation

```mermaid
sequenceDiagram
    participant UI as Client UI
    participant Agent as Connection Agent
    participant Tab as Tab Streamer

    UI->>Agent: Send "ready" signal
    Agent->>Tab: Forward ready signal
    Tab->>Tab: Create WebRTC offer
    Tab->>Agent: Send offer
    Agent->>UI: Forward offer
    UI->>UI: Create answer
    UI->>Agent: Send answer
    Agent->>Tab: Forward answer
    Tab->>UI: WebRTC connection established
```

The WebRTC peer-to-peer connection is negotiated through the WebSocket signaling channel.

### 5. Stream Begins

```mermaid
sequenceDiagram
    participant Tab as Tab Streamer
    participant UI as Client UI

    Tab->>Tab: getDisplayMedia() - capture screen
    Tab->>UI: Video stream via WebRTC
    UI->>UI: Display video and update status: "Connected"
```

The tab streamer captures the screen using `getDisplayMedia()` and streams it to the client UI.

## Page Reload Recovery Flow

When a page reload occurs (common during social login flows), the system automatically recovers:

### 1. Page Reload Occurs

```mermaid
sequenceDiagram
    participant User as User Action
    participant Browser as Remote Browser
    participant Tab as Tab Streamer

    User->>Browser: Click login button / form submission
    Browser->>Browser: Page reload/navigation begins
    Tab->>Tab: Current scripts lost
    Note over Tab: WebSocket and WebRTC connections drop
```

User interactions like clicking social login buttons cause page reloads that destroy the current scripts.

### 2. Automatic Script Re-injection

```mermaid
sequenceDiagram
    participant Browser as Remote Browser
    participant Tab as Tab Streamer

    Browser->>Browser: Page load complete
    Browser->>Tab: Auto-inject persistent script
    Note over Browser: addScriptToEvaluateOnNewDocument triggers
    Tab->>Tab: Persistent script executes
```

The persistent script registered with `addScriptToEvaluateOnNewDocument()` automatically executes on the new page.

### 3. Automatic Stream Restart

```mermaid
sequenceDiagram
    participant Tab as Tab Streamer (Persistent)

    Tab->>Tab: Check if tabStreamer available
    Tab->>Tab: Initialize streaming configuration
    Tab->>Tab: Start tab streamer automatically
    Note over Tab: getTabStreamerScriptForPersistentInjection logic
```

The persistent script (`getTabStreamerScriptForPersistentInjection`) automatically attempts to restart the tab streamer.

### 4. WebSocket Reconnection

```mermaid
sequenceDiagram
    participant Tab as Tab Streamer
    participant Agent as Connection Agent
    participant UI as Client UI

    Tab->>Agent: New WebSocket connection
    Tab->>Agent: Send "social-login-reconnected" signal
    Agent->>UI: Broadcast reconnection event
    UI->>UI: Show "Navigation detected" overlay
```

After the tab streamer starts, it establishes a new WebSocket connection and signals the reconnection.

### 5. UI Reconnection Signal

```mermaid
sequenceDiagram
    participant UI as Client UI
    participant Agent as Connection Agent

    UI->>UI: Receive reconnection signal
    UI->>Agent: Send "ready" signal for renegotiation
    Note over UI: Automatic WebRTC renegotiation begins
```

The UI receives the reconnection signal and automatically sends a ready signal to initiate WebRTC renegotiation.

### 6. WebRTC Renegotiation

```mermaid
sequenceDiagram
    participant UI as Client UI
    participant Agent as Connection Agent
    participant Tab as Tab Streamer

    UI->>Agent: Send "ready" signal
    Agent->>Tab: Forward ready signal
    Tab->>Tab: Create new WebRTC offer
    Tab->>Agent: Send new offer
    Agent->>UI: Forward offer
    UI->>UI: Create new answer
    UI->>Agent: Send answer
    Agent->>Tab: Forward answer
```

A new WebRTC connection is negotiated using the same process as the initial setup.

### 7. Stream Restored

```mermaid
sequenceDiagram
    participant Tab as Tab Streamer
    participant UI as Client UI

    Tab->>Tab: New getDisplayMedia() capture
    Tab->>UI: New video stream via WebRTC
    UI->>UI: Hide overlay, show video
    UI->>UI: Update status: "Connected"
```

The new stream becomes available and the UI automatically hides the loading overlay.

## Key Implementation Details

### Persistent Script Content

The `getTabStreamerScriptForPersistentInjection()` function creates a self-executing script that:

1. Checks if `window.tabStreamer` is available
2. Automatically initializes the tab streamer if not already streaming
3. Sends a reconnection signal to the Connection Agent
4. Handles errors gracefully and allows retries

### Timing Expectations

| Phase                   | Typical Duration |
| ----------------------- | ---------------- |
| Page reload             | 1-3 seconds      |
| Script re-injection     | 500ms-1s         |
| WebSocket reconnection  | 500ms-2s         |
| WebRTC renegotiation    | 2-4 seconds      |
| **Total recovery time** | **4-10 seconds** |

### Error Handling

The system handles common failure scenarios:

- **Script injection fails**: Retry with exponential backoff
- **WebSocket connection fails**: Automatic reconnection attempts
- **WebRTC negotiation fails**: Fallback to new offer/answer cycle
- **Media capture fails**: Request new display media stream

This flow ensures that video streaming continues seamlessly even when pages reload during complex social login flows, providing users with continuous visual feedback of the automation process.
